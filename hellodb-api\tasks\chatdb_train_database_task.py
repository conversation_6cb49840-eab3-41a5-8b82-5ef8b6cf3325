import datetime
from core.hellodb.hellodb import Hellodb
from core.hellodb.hellodb_factory import HellodbFactory
from core.vanna.exception import HellodbTrainDataException
from core.vanna.hellodb_vanna import HellodbVanna
from celery import shared_task  # 使用
from models.datasource import DataSourceApp
from services.datasource_service import DatasourceService

from core.vanna.hellodb_vanna_factory import HellodbVannaFactory
from config import Config
from openai import OpenAI
import logging


def split_text_by_delimiter(text: str, max_length: int, delimiter: str = ";\n") -> list[str]:
    """
    将文本按照指定分隔符分割成不超过最大长度的字符串数组

    Args:
        text: 要分割的文本
        max_length: 每个分割后字符串的最大长度
        delimiter: 分隔符，默认为 ";\n"

    Returns:
        分割后的字符串数组
    """
    if len(text) <= max_length:
        return [text]

    # 按分隔符分割文本
    segments = text.split(delimiter)

    chunks = []
    current_chunk = ""

    for segment in segments:
        # 如果单个段落就超过最大长度，直接作为一个chunk
        if len(segment) > max_length:
            # 先保存当前chunk（如果有内容）
            if current_chunk:
                chunks.append(current_chunk.rstrip())
                current_chunk = ""
            chunks.append(segment)
            continue

        # 检查添加当前段落后是否会超过最大长度
        test_chunk = current_chunk + (delimiter if current_chunk else "") + segment

        if len(test_chunk) <= max_length:
            # 不会超过，添加到当前chunk
            current_chunk = test_chunk
        else:
            # 会超过，保存当前chunk并开始新的chunk
            if current_chunk:
                chunks.append(current_chunk.rstrip())
            current_chunk = segment

    # 添加最后一个chunk
    if current_chunk:
        chunks.append(current_chunk.rstrip())

    return chunks

def get_vanna_client_old(datasourceApp: DataSourceApp = None) -> HellodbVanna:
    config = Config()
    VANNA_LLM_MODEL = config.VANNA_LLM_MODEL
    VANNA_LLM_API_KEY = config.VANNA_LLM_API_KEY
    VANNA_LLM_BASE_URL = config.VANNA_LLM_BASE_URL

    if datasourceApp is None:
        path = '/data/chroma/default'
        initial_prompt = ''
    else:
        path = f'/data/chroma/{datasourceApp.alias}'
        initial_prompt = datasourceApp.initial_prompt

    vanna_config = {
        'path' : path,
        'model': VANNA_LLM_MODEL,
        'api_key': VANNA_LLM_API_KEY,
        'initial_prompt': initial_prompt,
        'language': '中文'
    }

    client = OpenAI(api_key=VANNA_LLM_API_KEY, base_url=VANNA_LLM_BASE_URL)

    vn = HellodbVannaFactory.create_vanna(datasourceApp, client=client, config=vanna_config)

    return vn


def get_vanna_client(datasourceApp: DataSourceApp = None) -> Hellodb:
    return HellodbFactory.create_hellodb(datasourceApp)

# 定义 Celery 任务（模块级）
@shared_task(queue='chatdb')
def train_vanna_task(app_id, datasource_id, account_id):

    try:

        if not app_id:
            raise ValueError("Invalid input parameters, you must provide app_id at least.")

        app_info = DatasourceService.get_app_by_id(app_id)
        
        if not datasource_id:
            datasource_id = app_info.datasource_id

        if not datasource_id:
            raise ValueError("Invalid input parameters, you must provide datasource_id while datasource_id is not provided in app_info.")
        
        datasource = DatasourceService.get_datasource_by_id(datasource_id)

        logging.warning(f'''
Train Task Start At {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
app_id: {app_id}
datasource_id: {datasource_id}
executor_account: {account_id}
        ''')

        vn = get_vanna_client(app_info)
        vn.connect_to_database(datasource=datasource)


        ######################  训练数据库摘要  #############################
        try:
            database_summary = vn.get_database_summary(datasource.database)
            if len(database_summary) > 2048:
                # 太长，以不超过 2048 * 4 的长度分隔；但要以;\n为分隔符
                summary_chunks = split_text_by_delimiter(database_summary, max_length=2048, delimiter=";\n")
                for chunk in summary_chunks:
                    vn.train(documentation=chunk)
            else:
                # vn.train(database_summary=database_summary)
                vn.train(documentation=database_summary)
        except HellodbTrainDataException as e:
            logging.error(f"Failed to train database summary: {str(e)}")


        ######################  训练数据库结构  #############################
        df_information_schema = vn.get_information_schema(datasource.database)
        # This will break up the information schema into bite-sized chunks that can be referenced by the LLM

        # vn.train(df_information_schema)
        plan = vn.get_training_plan_generic(df_information_schema)
        vn.train(plan=plan)
        ######################  完成训练数据库结构  ############################# 

        logging.warning(f'''
Training complete At {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
app_id: {app_id}
datasource_id: {datasource_id}
executor_account: {account_id}
database_summary: {database_summary}
df_information_schema size: {len(df_information_schema)}
''')

        return {"message": f"Training completed for app: {app_id} and datasource: {datasource_id}"}
    except Exception as e:
        logging.error(f"Training failed for app {app_id}: {str(e)}")
        raise  # 让 Celery 记录任务失败