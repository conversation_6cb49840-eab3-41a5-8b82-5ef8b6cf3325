#!/bin/bash

set -e

USER_ID=${LOCAL_UID:-1000}
GROUP_ID=${LOCAL_GID:-1000}
USER_NAME=appuser

if id "$USER_NAME" >/dev/null 2>&1; then
    CURRENT_UID=$(id -u $USER_NAME)
    CURRENT_GID=$(id -g $USER_NAME)
    if [ "$CURRENT_UID" != "$USER_ID" ]; then
        usermod -u $USER_ID $USER_NAME
    fi
    if [ "$CURRENT_GID" != "$GROUP_ID" ]; then
        groupmod -g $GROUP_ID $USER_NAME
    fi
else
    groupadd -g $GROUP_ID $USER_NAME
    useradd -m -u $USER_ID -g $GROUP_ID $USER_NAME
fi

# 修正挂载目录权限（可选，推荐）
chown -R $USER_ID:$GROUP_ID /app/logs /app/data /app/chroma 2>/dev/null || true

# 检查license文件
if [[ -n "${HELLODB_LICENSE_PATH}" ]]; then
  echo "使用自定义license路径: ${HELLODB_LICENSE_PATH}"
  if [[ ! -f "${HELLODB_LICENSE_PATH}" ]]; then
    echo "警告: 在指定路径 ${HELLODB_LICENSE_PATH} 未找到license文件"
  fi
else
  # 默认license路径
  DEFAULT_LICENSE_PATH="/app/data/license.lic"
  
  # 检查默认路径
  if [[ -f "${DEFAULT_LICENSE_PATH}" ]]; then
    export HELLODB_LICENSE_PATH="${DEFAULT_LICENSE_PATH}"
    echo "使用默认license路径: ${HELLODB_LICENSE_PATH}"
  else
    echo "警告: 在默认路径 ${DEFAULT_LICENSE_PATH} 未找到license文件"
  fi
fi

if [[ "${MIGRATION_ENABLED}" == "true" ]]; then
  echo "Running migrations"
  flask db upgrade
fi

if [[ "${INIT_SYSTEM}" == "true" ]]; then
  echo "Initializing system..."
  flask init-system
fi

if [[ "${MODE}" == "worker" ]]; then
  exec su $USER_NAME -c "celery -A app.celery worker -P ${CELERY_WORKER_CLASS:-gevent} -c ${CELERY_WORKER_AMOUNT:-1} --loglevel ${LOG_LEVEL:-INFO} -Q ${CELERY_QUEUES:-chatdb,mail} --logfile=/app/logs/celery_worker.log"
elif [[ "${MODE}" == "beat" ]]; then
  exec su $USER_NAME -c "celery -A app.celery beat --loglevel ${LOG_LEVEL:-INFO} --logfile=/app/logs/celery_beat.log"
elif [[ "${MODE}" == "flower" ]]; then
  exec su $USER_NAME -c "celery  -A app.celery flower --loglevel ${LOG_LEVEL:-INFO} --address=0.0.0.0 --port=${CELERY_FLOWER_PORT:-5555} --url_prefix=${CELERY_FLOWER_URL_PREFIX:-flower} --basic-auth=user:pswd"
else
  if [[ "${DEBUG}" == "true" ]]; then
    exec su $USER_NAME -c "flask run --host=${APP_BIND_ADDRESS:-0.0.0.0} --port=${PORT:-5001} --debug"
  else
    exec su $USER_NAME -c "gunicorn --config gunicorn.conf.py app:app"
  fi
fi